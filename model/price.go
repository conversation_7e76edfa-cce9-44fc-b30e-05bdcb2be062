package model

type PriceAgent struct {
	ItemId       string             ` json:"item_id,omitempty"`
	ItemCode     string             ` json:"item_code,omitempty"`
	ItemName     string             ` json:"item_name,omitempty"`
	PriceTypeId  string             ` json:"price_type_id,omitempty"`
	Price        string             ` json:"price,omitempty"`
	TaxRate      string             ` json:"tax_rate,omitempty"`
	UseDate      string             ` json:"use_date,omitempty"`
	Hidden       bool               ` json:"hidden,omitempty"`
	TakeoutPrice string             ` json:"takeout_price,omitempty"`
	Modify       []*PriceModifyInfo ` json:"modify,omitempty"`
	ExtendFields map[string]interface{}
	// 是否可用
	IsValid bool ` json:"is_valid"`
}

type PriceModifyInfo struct {
	Price          string                 ` json:"price,omitempty"`
	TaxRate        string                 ` json:"tax_rate,omitempty"`
	UseDate        string                 ` json:"use_date,omitempty"`
	Hidden         bool                   ` json:"hidden,omitempty"`
	TakeoutPrice   string                 ` json:"takeout_price,omitempty"`
	EffectiveTime  string                 ` json:"effective_time,omitempty"`
	BatchId        string                 ` json:"batch_id,omitempty"`
	ExtendedFields map[string]interface{} ` json:"extended_fields,omitempty"`
	// 是否可用
	IsValid bool ` json:"is_valid"`
}

// PriceInfo 本地存储
type PriceInfo struct {
	Rows    []*PriceAgent `json:"rows"`
	Total   string        `json:"total,omitempty"`
	BatchId string        `json:"batch_id,omitempty"`
	// pull_time
	PullTime  string `json:"pull_time,omitempty"`
	VersionId string `json:"version_id,omitempty"`
}

type ModifyProductPriceRow struct {
	ItemId      string `protobuf:"varint,1,opt,name=item_id,json=itemId,proto3" json:"item_id,omitempty"`
	ItemCode    string `protobuf:"bytes,2,opt,name=item_code,json=itemCode,proto3" json:"item_code,omitempty"`
	ItemName    string `protobuf:"bytes,3,opt,name=item_name,json=itemName,proto3" json:"item_name,omitempty"`
	PriceTypeId string `protobuf:"varint,4,opt,name=price_type_id,json=priceTypeId,proto3" json:"price_type_id,omitempty"`
	BatchId     string `protobuf:"varint,5,opt,name=batch_id,json=batchId,proto3" json:"batch_id,omitempty"`
	// 修改信息
	Modify []*PriceModifyInfo `protobuf:"bytes,6,rep,name=modify,proto3" json:"modify,omitempty"`
	// 扩展字段
	ExtendFields map[string]interface{} `protobuf:"bytes,7,rep,name=extend_fields,json=extendFields,proto3" json:"extend_fields,omitempty"`
}

// PriceResp 云端查询响应
type PriceResp struct {
	Current *Current `json:"current"`
	Modify  *Modify  `json:"modify"`
	Total   string   `json:"total,omitempty"`
	BatchId string   `json:"batch_id,omitempty"`
	// pull_time
	PullTime  string `json:"pull_time,omitempty"`
	QueryType string `json:"query_type,omitempty"`
	VersionId string `json:"version_id,omitempty"`
}

type Modify struct {
	Items []*ModifyProductPriceRow `json:"items"`
}
type Current struct {
	Items []*PriceAgent `json:"items"`
}

type PriceType struct {
	ID            string           `json:"id"`
	PartnerID     string           `json:"partner_id"`
	ScopeID       string           `json:"scope_id"`
	ParentID      string           `json:"parent_id"`
	SchemaID      string           `json:"schema_id"`
	SchemaName    string           `json:"schema_name"`
	State         string           `json:"state"`
	Fields        *PriceTypeFields `json:"fields"`
	Created       string           `json:"created"`
	Updated       string           `json:"updated"`
	CreatedBy     string           `json:"created_by"`
	UpdatedBy     string           `json:"updated_by"`
	ProcessStatus string           `json:"process_status"`
}

type PriceTypeFields struct {
	Code            string   `json:"code"`
	Name            string   `json:"name"`
	ParentID        string   `json:"parent_id"`
	ChannelCodeList []string `json:"channel_code_list,omitempty"`
	ChannelCodeMap  string   `json:"channel_code_map,omitempty"`
}

type ModifyAgent struct {
	ItemId         string                 ` json:"item_id,omitempty"`
	ItemCode       string                 ` json:"item_code,omitempty"`
	ItemName       string                 ` json:"item_name,omitempty"`
	Price          string                 ` json:"price,omitempty"`
	TaxRate        string                 ` json:"tax_rate,omitempty"`
	UseDate        string                 ` json:"use_date,omitempty"`
	Hidden         bool                   ` json:"hidden,omitempty"`
	TakeoutPrice   string                 ` json:"takeout_price,omitempty"`
	EffectiveTime  string                 ` json:"effective_time,omitempty"`
	BatchId        string                 ` json:"batch_id,omitempty"`
	ExtendedFields map[string]interface{} ` json:"extended_fields,omitempty"`
	IsValid        bool                   ` json:"is_valid"`
}

type ModifyPriceInfo struct {
	Rows    []*PriceAgent `json:"rows"`
	Total   string        `json:"total,omitempty"`
	BatchId string        `json:"batch_id,omitempty"`
	// pull_time
	PullTime  string `json:"pull_time,omitempty"`
	VersionId string `json:"version_id,omitempty"`
}
