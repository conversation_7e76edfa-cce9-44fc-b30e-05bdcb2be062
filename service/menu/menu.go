package menu

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"time"

	"github.com/buger/jsonparser"
	"github.com/spf13/cast"
	"golang.org/x/sync/errgroup"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/pkg/log"
	"hexcloud.cn/hicloud/menukit/pkg/store"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
	"hexcloud.cn/hicloud/menukit/service/price"
)

// GetMenuParams represents parameters for getting menu data (kept for backward compatibility)
type GetMenuParams struct {
	StoreID          string // storeID: Store ID
	Lang             string // lang: Language
	ChannelCode      string // channelCode: Channel code
	PartnerID        string
	Host             string
	MaxRetries       int
	RetryDelay       time.Duration
	Token            string
	localData        *ProductListOpt      `json:"local_data"`
	queryType        model.PriceQueryType // queryType: Price query type
	batchID          string               // batchID: Batch ID
	ExpireInterval   time.Duration
	NotFilterByPrice bool
	ReadStore        store.Store
	WriteStore       store.Store
}

// BaseDataRequest represents the price query request
type BaseDataRequest struct {
	BatchID string `json:"batchId"`
	Channel string `json:"channel"`
	StoreID string `json:"storeId"`
	// Query type 0:current price 2:modify 3:all
	QueryType               string `json:"queryType"`
	OriginProductListMd5    string `json:"originProductListMd5"`
	OriginProductSpuInfoMd5 string `json:"originProductSpuInfoMd5"`
	OriginAttrListMd5       string `json:"originAttrListMd5"`
	OriginPriceTypeMd5      string `json:"originPriceTypeMd5"`
}

// Page represents pagination information
type Page struct {
	Limit  int `json:"limit,omitempty"`
	Offset int `json:"offset,omitempty"`
}

// Default retry configuration
const (
	DefaultMaxRetries     = 3
	DefaultRetryDelay     = 500 * time.Millisecond
	DefaultExpireInterval = 7 * time.Hour * 24
)

// sendBaseDataRequest sends a price query request and handles retry logic
// url: request URL
// req: request body
// token: authentication token
// maxRetries: maximum retry attempts
// retryDelay: retry interval time
func sendBaseDataRequest(ctx context.Context, params *GetMenuParams) ([]byte, error) {
	// 序列化请求体
	req := &BaseDataRequest{
		BatchID:                 params.batchID,
		QueryType:               cast.ToString(int(params.queryType)),
		OriginProductListMd5:    *params.localData.OriginProductListMd5,
		OriginProductSpuInfoMd5: *params.localData.OriginProductSpuInfoMd5,
		OriginAttrListMd5:       *params.localData.OriginAttrListMd5,
		OriginPriceTypeMd5:      *params.localData.OriginPriceTypeMd5,
		Channel:                 params.ChannelCode,
		StoreID:                 params.StoreID,
	}
	url := fmt.Sprintf("%s%s", params.Host, model.DefaultBaseDataAPi)

	reqBody, err := json.Marshal(req)
	if err != nil {
		return nil, fmt.Errorf("序列化请求失败: %w", err)
	}

	// 重试逻辑
	var respBody []byte
	var lastErr error

	for i := 0; i <= params.MaxRetries; i++ {
		// 如果不是第一次尝试，等待一段时间再重试
		if i > 0 {
			time.Sleep(params.RetryDelay)
		}
		// 创建HTTP请求
		httpReq, err := http.NewRequestWithContext(ctx, "POST", url, bytes.NewBuffer(reqBody))
		if err != nil {
			return nil, fmt.Errorf("创建HTTP请求失败: %w", err)
		}

		// 设置请求头
		httpReq.Header.Set("Content-Type", "application/json")

		httpReq.Header.Set("Authorization", fmt.Sprintf("Bearer %s", params.Token))

		// 设置语言

		httpReq.Header.Set("Accept-Language", params.Lang)

		// 设置租户

		httpReq.Header.Set("partner_id", params.PartnerID)

		respBody, err = getHttpResponse(httpReq, model.GlobalHTTPClient)
		if err != nil {
			lastErr = err
			continue
		}
		return respBody, nil
	}

	// 所有重试都失败了，返回最后一个错误
	return nil, lastErr
}

// PullBaseData pulls menu base information from API
// host: API host address
// token: authentication token
// storeID: store ID
// saveFunc: function to save price information
// getFunc: function to get existing price information
func PullBaseData(ctx context.Context, params *GetMenuParams) (*ProductListOpt, error) {
	// Get batch ID and query type
	var batchIDStr string
	var queryType model.PriceQueryType
	currentPriceInfo := params.localData.PriceInfo
	batchIDStr, queryType, err := price.GetBatchIdAndQueryType(currentPriceInfo, params.ExpireInterval)
	if err != nil {
		log.Logger.Errorf("Failed to get batch ID and query type: %v, changed to full fetch (may be first fetch)", err)
	}

	// Convert string batch ID to uint64 for params
	params.batchID = batchIDStr
	params.queryType = queryType

	start := time.Now()
	respBody, err := sendBaseDataRequest(ctx, params)
	if err != nil {
		return nil, err
	}
	log.Logger.Infof("Fetch base cloud data: baseData, size: %s, time: %v", utils.FormatDataSize(len(respBody)), time.Since(start))

	priceInfo := &model.PriceInfo{}
	priceVersionInfo := &model.StorePriceVersionInfoResponse{}
	g, _ := errgroup.WithContext(ctx)
	g.Go(func() error {
		var err error
		var hasUpdated bool
		priceInfo, hasUpdated, err = price.BuildPriceInfoByResponse(respBody, currentPriceInfo, params.queryType)
		if err != nil {
			return err
		}
		if hasUpdated {
			priceInfoBs, err := json.Marshal(priceInfo)
			if err != nil {
				return err
			}
			if err := params.WriteStore.Save(priceInfoBs, model.DefaultPriceFileName); err != nil {
				return fmt.Errorf("failed to save price information: %w", err)
			}
		}
		return err
	})
	g.Wait()
	params.localData.PriceVersionInfo = priceVersionInfo
	params.localData, err = buildAndSaveBaseData(ctx, params.localData, respBody, params.WriteStore.Save)
	if err != nil {
		return nil, err
	}
	params.localData.PriceInfo = priceInfo
	params.localData.PriceVersionInfo = priceVersionInfo
	return params.localData, nil
}

// GetMenuDataLegacy gets menu data from file or cloud (legacy function for backward compatibility)
func GetMenuDataLegacy(ctx context.Context, params *GetMenuParams) (*model.ProductListResponse, error) {
	// Apply option configuration

	var menuOpts *ProductListOpt
	var err error
	//	opts := applyPullPriceOptions(options...)
	log.Logger.Infof("Start getting menu data - Store ID: %s, Channel: %s", params.StoreID, params.ChannelCode)

	// Try to fetch data from cloud first
	start := time.Now()
	//{
	//    "batchId": "5020927675187920896",
	//    "channel": "POS",params
	//    "storeId": "4972806615293067264",
	//    "queryType": "2",
	//    "originProductListMd5":"89de39d677222fbb2f278053146023d4",
	//    "originProductSpuInfoMd5":"185e2bf66567f6bc9efc651436da0406",
	//    "originAttrListMd5":"4aa46649699733c0edf6386cc5e6523b",
	//    "originPriceTypeMd5":"a291c2d9a884075147337328a12fddb6"
	//}
	params.localData, err = LoadLocalMenuData(ctx, params)
	log.Logger.Infof("LoadLocalMenuData time: %v", time.Since(start))
	if err != nil {
		log.Logger.Errorf("Local cache read failed: local error=%v", err)
	}
	menuOpts, err = PullBaseData(ctx, params)
	elapsed := time.Since(start)
	log.Logger.Infof("Cloud data fetch time: %s", elapsed.String())
	if err != nil {
		log.Logger.Warnf("Failed to fetch cloud data: %v, trying to use local cache data", err)
		// Use err group to read local data concurrently
		menuOpts = params.localData
		log.Logger.Info("Successfully used local cache data")
	} else {
		log.Logger.Info("Successfully fetched data from cloud")
	}

	// Generate channel product list
	start = time.Now()
	menuOpts.NotFilterByPrice = params.NotFilterByPrice

	result, err := GetChannelProductList(ctx, params.ChannelCode, menuOpts)
	log.Logger.Infof("Assembly time: %v", time.Since(start))
	if err != nil {
		return nil, fmt.Errorf("failed to generate channel product list: %w", err)
	}

	return result, nil
}

// Define file read tasks
type fileTask struct {
	fileName        string
	unmarshalTarget any
	name            string
	md5String       *string
}

// BuildFromLocal assembles menu data purely from local files using provided getFunc.
// It mirrors LoadLocalMenuData + GetChannelProductList, exposing a public API for prod-only usage.
func BuildFromLocal(ctx context.Context, store store.Store, channelCode string, notFilterByPrice bool) (*model.ProductListResponse, error) {
	params := &GetMenuParams{
		ReadStore:        store,
		NotFilterByPrice: notFilterByPrice,
	}
	menuOpts, err := LoadLocalMenuData(ctx, params)
	if err != nil {
		return nil, err
	}
	menuOpts.NotFilterByPrice = notFilterByPrice
	return GetChannelProductList(ctx, channelCode, menuOpts)
}

// LoadLocalMenuData loads local menu data concurrently
func LoadLocalMenuData(ctx context.Context, params *GetMenuParams) (*ProductListOpt, error) {
	menuOpts := &ProductListOpt{
		ProductList:             make([]*model.Category, 0),
		ProductInfo:             make([]*model.Product, 0),
		ProductAttr:             make([]*model.AdditionalAttribute, 0),
		PriceInfo:               &model.PriceInfo{},
		PriceTypeInfo:           make([]*model.PriceType, 0),
		StockList:               make([]map[string]interface{}, 0),
		OriginPriceTypeMd5:      new(string),
		OriginProductSpuInfoMd5: new(string),
		OriginAttrListMd5:       new(string),
		OriginProductListMd5:    new(string),
		OriginStockListMd5:      new(string),
	}
	g, ctx := errgroup.WithContext(ctx)
	tasks := []fileTask{
		{model.DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "price_type", menuOpts.OriginPriceTypeMd5},
		{model.DefaultProductInfoFileName, &menuOpts.ProductInfo, "product_info", menuOpts.OriginProductSpuInfoMd5},
		{model.DefaultProductAttrFileName, &menuOpts.ProductAttr, "product_attr", menuOpts.OriginAttrListMd5},
		{model.DefaultProductListFileName, &menuOpts.ProductList, "product_list", menuOpts.OriginProductListMd5},
		{model.DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}

	// 单独处理价格信息
	g.Go(func() error {
		select {
		case <-ctx.Done():
			return fmt.Errorf("context cancelled before loading price info: %w", ctx.Err())
		default:
			data, err := price.GetPriceInfoFromFile(0, params.ReadStore, params.WriteStore)
			if err != nil {
				return fmt.Errorf("failed to read %s file: %w", "price", err)
			}
			menuOpts.PriceInfo = data
			return nil
		}
	})

	// 并发读取所有本地文件
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return fmt.Errorf("context cancelled before loading %s: %w", task.name, ctx.Err())
			default:
				start := time.Now()
				data, err := params.ReadStore.Get(task.fileName)
				if err != nil {
					return fmt.Errorf("failed to read %s file: %w", task.name, err)
				}
				md5, _ := jsonparser.GetString(data, "md5")
				*task.md5String = md5
				data, _, _, _ = jsonparser.Get(data, "rows")
				if len(data) == 0 {
					log.Logger.Infof("local file %s is empty, skipping", task.name)
					return nil
				}
				err = json.Unmarshal(data, task.unmarshalTarget)
				if err != nil {
					return fmt.Errorf("failed to parse %s file: %w", task.name, err)
				}
				log.Logger.Infof("successfully read local file: %s, size: %s, time: %v", task.name, utils.FormatDataSize(len(data)), time.Since(start))
				return nil
			}
		})
	}

	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("failed to read local files concurrently: %w", err)
	}

	log.Logger.Info("all local files read successfully")
	return menuOpts, nil
}

// Get and other information (product_info product_attr price_type product_list) return map[string][]byte filename and content
func buildAndSaveBaseData(ctx context.Context, menuOpts *ProductListOpt, resp []byte, savefunc func([]byte, string) error) (*ProductListOpt, error) {
	if menuOpts == nil {
		menuOpts = &ProductListOpt{
			ProductList:   make([]*model.Category, 0),
			ProductInfo:   make([]*model.Product, 0),
			ProductAttr:   make([]*model.AdditionalAttribute, 0),
			PriceInfo:     &model.PriceInfo{},
			PriceTypeInfo: make([]*model.PriceType, 0),
		}
	}
	tasks := []fileTask{
		{model.DefaultPriceTypeFileName, &menuOpts.PriceTypeInfo, "priceType", menuOpts.OriginPriceTypeMd5},
		{model.DefaultProductInfoFileName, &menuOpts.ProductInfo, "productSpuInfo", menuOpts.OriginProductSpuInfoMd5},
		{model.DefaultProductAttrFileName, &menuOpts.ProductAttr, "attrList", menuOpts.OriginAttrListMd5},
		{model.DefaultProductListFileName, &menuOpts.ProductList, "productList", menuOpts.OriginProductListMd5},
		{model.DefaultStockListFileName, &menuOpts.StockList, "stockList", menuOpts.OriginStockListMd5},
	}
	g, ctx := errgroup.WithContext(ctx)
	for _, task := range tasks {
		task := task // 避免闭包问题
		g.Go(func() error {
			select {
			case <-ctx.Done():
				return fmt.Errorf("context cancelled before processing %s: %w", task.name, ctx.Err())
			default:
				data, _, _, err := jsonparser.Get(resp, "payload", task.name)
				if err != nil {
					return fmt.Errorf("failed to get %s: %w", task.name, err)
				}
				rows, _, _, err := jsonparser.Get(data, "rows")
				if err != nil {
					return fmt.Errorf("failed to get %s.rows: %w", task.name, err)
				}
				if cast.ToString(rows) == "null" {
					log.Logger.Infof("%s: local data is consistent with cloud data, using local data", task.name)
					return nil
				}
				err = json.Unmarshal(rows, task.unmarshalTarget)
				log.Logger.Infof("successfully read cloud data: %s, size: %s", task.name, utils.FormatDataSize(len(data)))
				if err := savefunc(data, task.fileName); err != nil {
					return fmt.Errorf("failed to save %s: %w", task.name, err)
				}
				return nil
			}
		})
	}
	// 等待所有goroutine完成
	if err := g.Wait(); err != nil {
		return menuOpts, fmt.Errorf("failed to read cloud data concurrently: %w", err)
	}

	return menuOpts, nil
}

func getHttpResponse(httpReq *http.Request, client *http.Client) ([]byte, error) {
	// Send request
	resp, err := client.Do(httpReq)
	if err != nil {
		return nil, fmt.Errorf("failed to send HTTP request: %w", err)
	}
	defer resp.Body.Close() // Ensure response body is closed before function exits

	// Check status code
	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("API returned error status code: %d", resp.StatusCode)
	}

	// Read response body
	respBody, err := io.ReadAll(resp.Body)
	if err != nil {
		return nil, fmt.Errorf("failed to read response: %w", err)
	}

	// Check status_code
	statusCode, err := jsonparser.GetInt(respBody, "status_code")
	if err != nil {
		return nil, fmt.Errorf("failed to read response status code: %w", err)
	}
	if statusCode != 200 {
		return nil, fmt.Errorf("API returned error status code: %d, resp:%s", statusCode, respBody)
	}
	// Request successful, break loop
	return respBody, nil

}
