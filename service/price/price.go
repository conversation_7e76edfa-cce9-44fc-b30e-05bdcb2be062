package price

import (
	"context"
	"errors"
	"fmt"
	"time"

	"github.com/buger/jsonparser"
	"github.com/goccy/go-json"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/pkg/log"
	"hexcloud.cn/hicloud/menukit/pkg/store"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
)

func GetPriceInfoFromFile(queryTime int64, readStore, writeStore store.Store) (*model.PriceInfo, error) {
	// 读取文件内容
	start := time.Now()
	bs, err := readStore.Get(model.DefaultPriceFileName)
	if err != nil {
		return nil, fmt.Errorf("读取价格信息文件失败: %w", err)
	}

	priceInfo := &model.PriceInfo{}

	if len(bs) == 0 {
		log.Logger.Infof("本地文件price为空,跳过")
		return priceInfo, nil
	}

	if err := json.Unmarshal(bs, priceInfo); err != nil {
		return nil, fmt.Errorf("解析价格信息失败: %w", err)
	}
	log.Logger.Infof("成功读取本地文件: price，大小: %s,耗时：%v", utils.FormatDataSize(len(bs)), time.Since(start))

	// 处理当前生效的modify
	priceInfoModel, hasEffective, err := checkEffective(priceInfo, queryTime)
	if err != nil {
		return nil, fmt.Errorf("检查价格生效状态失败: %w", err)
	}
	if queryTime == 0 && hasEffective {
		bs, err = json.Marshal(priceInfoModel)
		if err := writeStore.Save(bs, model.DefaultPriceFileName); err != nil {
			return nil, fmt.Errorf("保存价格信息失败: %w", err)
		}
	}
	return priceInfoModel, nil
}

// BuildPriceInfoByResponse 根据API响应构建价格信息
// respBody: API响应体
// currentInfo: 当前价格信息（可能为nil）
// queryType: 查询类型
func BuildPriceInfoByResponse(respBody []byte, currentInfo *model.PriceInfo, queryType model.PriceQueryType) (*model.PriceInfo, bool, error) {
	// 检查响应是否为空
	hasUpdated := false
	if len(respBody) == 0 {
		return nil, false, errors.New("响应体为空")
	}

	// 使用jsonparser解析payload部分
	payload, _, _, err := jsonparser.Get(respBody, "payload", "priceCenter")
	if err != nil {
		return nil, false, fmt.Errorf("解析payload字段失败: %w", err)
	}
	if len(payload) == 0 {
		return nil, false, errors.New("响应中缺少payload.priceCenter字段")
	}
	log.Logger.Infof("成功获取云端数据: price，大小: %s", utils.FormatDataSize(len(payload)))
	priceResp := &model.PriceResp{}
	err = json.Unmarshal(payload, priceResp)
	if err != nil {
		return nil, false, fmt.Errorf("解析priceCenter失败: %w", err)
	}

	// 获取拉取时间
	if priceResp.PullTime == "" {
		return nil, false, errors.New("响应中缺少pull_time字段")
	}

	// 获取查询类型
	if qt, ok := model.PriceQueryTypeMap[priceResp.QueryType]; ok {
		queryType = qt
	}

	// 初始化结果
	result := currentInfo

	switch queryType {
	// 全量查询 直接覆盖
	case model.PriceQueryTypeAll, model.PriceQueryTypeCurrent:
		if priceResp.Current != nil {
			result.Rows = priceResp.Current.Items
			hasUpdated = true
		}
		// 只查询modify 则合并
	case model.PriceQueryTypeModify:
		// 查询修改价格，提取 modify 部分
		if priceResp.Modify == nil {
			log.Logger.Infof("price:本地数据和云端数据一致，使用本地数据")
		}
		if priceResp.Modify != nil {
			result, err = mergeModifyIntoFile(result, priceResp.Modify.Items)
			if err != nil {
				return nil, hasUpdated, fmt.Errorf("合并修改信息失败: %w", err)
			}
			// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
			hasUpdated = true
		}

	default:
		return nil, hasUpdated, errors.New("不支持的查询类型")
	}
	result, hasEffective, err := checkEffective(result, 0)
	if err != nil {
		return nil, hasUpdated, fmt.Errorf("检查价格生效状态失败: %w", err)
	}
	if hasEffective {
		hasUpdated = true
	}

	// 设置批次ID和拉取时间
	result.BatchId = priceResp.BatchId
	result.PullTime = priceResp.PullTime
	result.VersionId = priceResp.VersionId

	return result, hasUpdated, nil
}

func handleCanceledBatches(result *model.PriceInfo, ids []string) *model.PriceInfo {
	if len(ids) == 0 {
		return result
	}
	cancelMap := make(map[string]struct{}, len(ids))
	for _, id := range ids {
		cancelMap[id] = struct{}{}
	}
	for _, row := range result.Rows {
		if len(row.Modify) == 0 {
			continue
		}
		modify := make([]*model.PriceModifyInfo, 0, len(row.Modify))
		for _, m := range row.Modify {
			if _, ok := cancelMap[m.BatchId]; !ok {
				modify = append(modify, m)
			}
		}
		row.Modify = modify
	}
	return result
}

// mergeModifyIntoFile 合并当前信息和修改信息
func mergeModifyIntoFile(currentInfo *model.PriceInfo, modifyItems []*model.ModifyProductPriceRow) (*model.PriceInfo, error) {
	// 检查输入
	if len(modifyItems) == 0 {
		return currentInfo, nil
	}

	var err error

	// 合并项目
	currentInfo.Rows, err = mergeItems(currentInfo.Rows, modifyItems)
	if err != nil {
		return nil, fmt.Errorf("合并项目失败: %w", err)
	}
	return currentInfo, err
}

// mergeItems 合并两个项目数组，以item_id+price_type_id为键
// 如果修改项目中有相同的item_id，则替换当前项目中的相应项目
func mergeItems(currentItems []*model.PriceAgent, modifyItems []*model.ModifyProductPriceRow) ([]*model.PriceAgent, error) {

	if len(modifyItems) == 0 {
		return currentItems, nil
	}

	// 将当前项目转换为map，以item_id为键
	currentItemsMap := make(map[string]int)

	for i, item := range currentItems {
		itemID := item.ItemId
		priceTypeID := item.PriceTypeId
		key := fmt.Sprintf("%s-%s", itemID, priceTypeID)
		currentItemsMap[key] = i
	}

	// 处理修改项目，更新或添加到map中

	for _, modifyItem := range modifyItems {
		key := fmt.Sprintf("%s-%s", modifyItem.ItemId, modifyItem.PriceTypeId)

		// 检查当前项目中是否已存在该item_id-price_type_id
		idx, exists := currentItemsMap[key]
		if exists {
			// 如果存在，将修改项目的modify字段合并到当前项目的modify后面
			currentItems[idx].Modify = append(currentItems[idx].Modify, modifyItem.Modify...)
		}
		if !exists {
			// 如果不存在，直接添加修改项目
			currentItems = append(currentItems, &model.PriceAgent{
				ItemId:      modifyItem.ItemId,
				ItemCode:    modifyItem.ItemCode,
				ItemName:    modifyItem.ItemName,
				PriceTypeId: modifyItem.PriceTypeId,
				Modify:      modifyItem.Modify,
			})

		}
	}

	return currentItems, nil
}

func GetBatchIdAndQueryType(content *model.PriceInfo, expireInterval time.Duration) (batchId string, queryType model.PriceQueryType, err error) {

	//默认拉取全量
	batchId = content.BatchId
	queryType = model.PriceQueryTypeAll
	if content == nil {
		return
	}
	if content.PullTime == "" {
		return
	}
	pullTime, err := time.Parse(time.RFC3339, content.PullTime)
	if err != nil {
		log.Logger.Errorf("解析pull_time失败: %v", err)
		return
	}
	// 如果距离上次拉取时间超过3天，则重新拉取完整价格信息
	if time.Since(pullTime) > expireInterval {
		batchId = "0"
	}

	if batchId != "0" && batchId != "" {
		queryType = model.PriceQueryTypeModify
	}
	return
}

// 增量查询时 需要检查modify 元素是否已生效，如果已生效则更新rows，并删除modify中的元素
func checkEffective(priceInfo *model.PriceInfo, queryTime int64) (*model.PriceInfo, bool, error) {
	hasEffective := false
	// 当前时间
	if queryTime == 0 {
		queryTime = time.Now().Unix()
	}
	//cancelMap := make(map[string]struct{}, len(priceInfo.CancelBatchIds))
	//for _, id := range priceInfo.CancelBatchIds {
	//	cancelMap[id] = struct{}{}
	//}

	qt := time.Unix(queryTime, 0)

	// 遍历所有项目，检查modify元素是否已生效
	for _, row := range priceInfo.Rows {
		// 获取modify字段

		// 如果没有modify元素，跳过
		if len(row.Modify) == 0 {
			continue
		}

		// 检查每个modify元素是否已生效
		var effectiveModify *model.PriceModifyInfo
		// 最近生效的时间
		var latestEffectiveTime time.Time
		remainingModifies := make([]*model.PriceModifyInfo, 0, len(row.Modify))

		for _, modifyItem := range row.Modify {
			// 解析effective_time
			effectiveTime, err := time.Parse(time.RFC3339, modifyItem.EffectiveTime)
			if err != nil {
				return nil, false, fmt.Errorf("解析effective_time字段失败: %w", err)
			}
			//if _, ok := cancelMap[modifyItem.BatchId]; ok {
			//	continue
			//}
			// 检查是否已生效
			if qt.After(effectiveTime) || qt.Equal(effectiveTime) {
				// 已生效，添加到effectiveModifies
				if effectiveTime.After(latestEffectiveTime) {
					effectiveModify = modifyItem
					latestEffectiveTime = effectiveTime
					hasEffective = true
				}
			} else {
				// 未生效，保留该modify元素
				remainingModifies = append(remainingModifies, modifyItem)
			}
		}

		// 如果有已生效的modify元素，更新当前项目
		if effectiveModify != nil {
			//Price:        effectiveModify.Price,
			//				TaxRate:      effectiveModify.TaxRate,
			//				UseDate:      effectiveModify.UseDate,
			//				Hidden:       effectiveModify.Hidden,
			//				TakeoutPrice: effectiveModify.TakeoutPrice,
			row.Price = effectiveModify.Price
			row.TaxRate = effectiveModify.TaxRate
			row.UseDate = effectiveModify.UseDate
			row.Hidden = effectiveModify.Hidden
			row.TakeoutPrice = effectiveModify.TakeoutPrice
			row.Modify = remainingModifies
			row.ExtendFields = effectiveModify.ExtendedFields
			row.IsValid = effectiveModify.IsValid
		}
	}
	return priceInfo, hasEffective, nil
}

func MergeModifyFile(ctx context.Context, readStore, writeStore store.Store) error {
	// todo
	panic("implement me")
}
