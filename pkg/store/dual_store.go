package store

import "path/filepath"

// EnvStore wraps a Store with an environment prefix (e.g., "staging" / "prod")
type EnvStore struct {
	env   string
	store Store
}

func (e *EnvStore) Delete(key string) error {
	//TODO implement me
	panic("implement me")
}

func NewEnvStore(env string, s Store) *EnvStore {
	return &EnvStore{env: env, store: s}
}

func (e *EnvStore) Save(data []byte, key string) error {
	return e.store.Save(data, e.wrapKey(key))
}

func (e *EnvStore) Get(key string) ([]byte, error) {
	return e.store.Get(e.wrapKey(key))
}
func (e *EnvStore) Config() *StoreConfig {
	cfg := e.store.Config()
	if cfg.Options == nil {
		cfg.Options = make(map[string]interface{})
	}
	cfg.Options["env"] = e.env
	return cfg
}

func (e *EnvStore) wrapKey(key string) string {
	return filepath.Join(e.env, key)
}

// DualStore explicitly separates staging and production stores
type DualStore struct {
	Staging Store
	Prod    Store
}

func NewDualStore(base Store, prodDir, tempDir string) *DualStore {
	return &DualStore{
		Staging: NewEnvStore(tempDir, base),
		Prod:    NewEnvStore(prodDir, base),
	}
}
