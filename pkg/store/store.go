package store

// Store interface provides methods for getting and saving data
type Store interface {
	// Get retrieves data by key
	Get(key string) ([]byte, error)

	// Save stores data with key
	Save(data []byte, key string) error

	// Delete removes data by key
	Delete(key string) error

	// Config returns the store configuration
	Config() *StoreConfig
}

// StoreConfig represents the configuration for a store
type StoreConfig struct {
	// Type of store (e.g., "file", "memory", "database")
	Type string

	// Base directory or connection string
	Path string

	// Whether the store is read-only
	ReadOnly bool

	// Other store-specific options
	Options map[string]interface{}
}
