// Package menukit provides functionality for retrieving and processing
// multi-channel product menu data. It supports fetching data from cloud APIs
// and offers local caching mechanisms for improved performance and reliability.
package menukit

import (
	"context"
	"io"
	"os"
	"path/filepath"
	"sync"
	"time"

	"github.com/buger/jsonparser"
	"hexcloud.cn/hicloud/menukit/model"
	"hexcloud.cn/hicloud/menukit/pkg/store"
	"hexcloud.cn/hicloud/menukit/pkg/utils"
	"hexcloud.cn/hicloud/menukit/service/menu"
)

var GlobalMutex = sync.RWMutex{}

// GetDataMode represents the data retrieval mode
type GetDataMode int

const (
	ModeAutoMerge GetDataMode = iota // Automatically fetch and merge data
	ModeFetchOnly                    // Only fetch to temporary storage
	ModeMergeTemp                    // Merge temporary data to production
	ModeProdOnly                     // Only use production data
)

// Options configures the menu data retrieval
type Options struct {
	// Basic configuration
	host        string // API host address
	token       string // Authentication token
	storeID     string // Store ID
	channelCode string // Channel code
	lang        string // Language
	partnerID   string

	// Directory configuration
	prodDir string // Production data directory
	tempDir string // Temporary data directory

	// Operation mode
	mode      GetDataMode // Data retrieval mode
	autoMerge bool        // Whether to auto-merge

	// Retry configuration
	maxRetries int           // Maximum retry attempts
	retryDelay time.Duration // Retry delay time

	// Other configuration
	expireInterval time.Duration // Data expiration interval

	// Store for data operations
	store     store.Store      // Store interface for data operations
	dualStore *store.DualStore // Dual store for data operations
	//retun configuration
	returnBaseData   bool
	notFilterByPrice bool
}

// Option configures Options
type Option func(*Options)

// WithHost sets the API host address
func WithHost(host string) Option {
	return func(opts *Options) {
		opts.host = host
	}
}

// WithToken sets the authentication token
func WithToken(token string) Option {
	return func(opts *Options) {
		opts.token = token
	}
}

// WithStoreID sets the store ID
func WithStoreID(storeID string) Option {
	return func(opts *Options) {
		opts.storeID = storeID
	}
}

// WithChannelCode sets the channel code
func WithChannelCode(channelCode string) Option {
	return func(opts *Options) {
		opts.channelCode = channelCode
	}
}

// WithLang sets the language
func WithLang(lang string) Option {
	return func(opts *Options) {
		opts.lang = lang
	}
}

// WithProdDir sets the production data directory
func WithProdDir(dir string) Option {
	return func(opts *Options) {
		opts.prodDir = dir
	}
}

// WithTempDir sets the temporary data directory
func WithTempDir(dir string) Option {
	return func(opts *Options) {
		opts.tempDir = dir
	}
}

// WithMode sets the data retrieval mode
func WithMode(mode GetDataMode) Option {
	return func(opts *Options) {
		opts.mode = mode
	}
}

// WithAutoMerge sets whether to auto-merge
func WithAutoMerge(autoMerge bool) Option {
	return func(opts *Options) {
		opts.autoMerge = autoMerge
	}
}

// WithExpireInterval sets the data expiration interval
func WithExpireInterval(expireInterval time.Duration) Option {
	return func(opts *Options) {
		opts.expireInterval = expireInterval
	}
}

// WithMaxRetries sets the maximum retry attempts
func WithMaxRetries(maxRetries int) Option {
	return func(opts *Options) {
		opts.maxRetries = maxRetries
	}
}

// WithRetryDelay sets the retry delay time
func WithRetryDelay(retryDelay time.Duration) Option {
	return func(opts *Options) {
		opts.retryDelay = retryDelay
	}
}

func WithPartnerID(partnerID string) Option {
	return func(opts *Options) {
		opts.partnerID = partnerID
	}
}

func WithReturnBaseData(returnBaseData bool) Option {
	return func(o *Options) {
		o.returnBaseData = returnBaseData
	}
}
func WithNotFilterByPrice(notFilterByPrice bool) Option {
	return func(o *Options) {
		o.notFilterByPrice = notFilterByPrice
	}
}

// Getters for Options fields
func (o *Options) Host() string {
	return o.host
}

func (o *Options) Token() string {
	return o.token
}

func (o *Options) StoreID() string {
	return o.storeID
}

func (o *Options) ChannelCode() string {
	return o.channelCode
}

func (o *Options) Lang() string {
	return o.lang
}

func (o *Options) ProdDir() string {
	return o.prodDir
}

func (o *Options) TempDir() string {
	return o.tempDir
}

func (o *Options) Mode() GetDataMode {
	return o.mode
}

func (o *Options) AutoMerge() bool {
	return o.autoMerge
}

func (o *Options) MaxRetries() int {
	return o.maxRetries
}

func (o *Options) RetryDelay() time.Duration {
	return o.retryDelay
}

func (o *Options) ExpireInterval() time.Duration {
	return o.expireInterval
}
func (o *Options) Store() store.Store {
	return o.store
}

// WithStore sets the store for data operations
func WithStore(store store.Store) Option {
	return func(opts *Options) {
		opts.store = store
	}
}

// GetMenuData retrieves menu data with the specified options
func GetMenuData(ctx context.Context, options ...Option) (*model.ProductListResponse, error) {
	// Default options
	opts := &Options{
		host:           model.DefaultHost,
		prodDir:        "./prod",
		tempDir:        "./temp",
		mode:           ModeAutoMerge,
		maxRetries:     3,
		retryDelay:     500 * time.Millisecond,
		expireInterval: 7 * 24 * time.Hour,
		store:          store.NewFileStore("./"),
	}
	// Apply options
	for _, option := range options {
		option(opts)
	}
	//dualStore
	opts.dualStore = store.NewDualStore(opts.store, opts.prodDir, opts.tempDir)

	//处理
	// Execute based on mode
	switch opts.Mode() {
	case ModeAutoMerge:
		return getMenuDataAutoMerge(ctx, options...)
	case ModeFetchOnly:
		return nil, FetchToTemp(ctx, options...)
	case ModeMergeTemp:
		return nil, MergeTempToProd(ctx, options...)
	case ModeProdOnly:
		return GetMenuDataFromProd(ctx, options...)
	default:
		return getMenuDataAutoMerge(ctx, options...)
	}
}

// GetMenuDataFromProd retrieves menu data from production directory only
func GetMenuDataFromProd(ctx context.Context, options ...Option) (*model.ProductListResponse, error) {
	// Prod-only: read from prodDir via FileStore and assemble locally
	opts := applyOptions(options...)
	return menu.BuildFromLocal(ctx, opts.store, opts.channelCode, opts.notFilterByPrice)
}

// FetchToTemp fetches data to temporary directory only
func FetchToTemp(ctx context.Context, options ...Option) error {
	// Use tempDir for saving fetched data
	opts := applyOptions(options...)
	params := optionsToParams(opts)
	_, err := menu.GetMenuDataLegacy(ctx, params)
	return err
}

// MergeTempToProd merges temporary data to production directory
func MergeTempToProd(ctx context.Context, options ...Option) error {
	opts := applyOptions(options...)
	// Copy known files from temp to prod
	files := []string{
		model.DefaultProductListFileName,
		model.DefaultProductInfoFileName,
		model.DefaultProductAttrFileName,
		model.DefaultPriceTypeFileName,
		model.DefaultStockListFileName,
		model.DefaultPriceFileName,
		model.DefaultPriceVersionFileName,
	}
	for _, name := range files {
		src := utils.GetFilePath(opts.tempDir, name)
		dst := utils.GetFilePath(opts.prodDir, name)
		if err := copyFileAtomic(src, dst); err != nil {
			return err
		}
		// clear cache
		store.ClearFileCache(dst)
	}
	return nil
}

// copyFileAtomic copies a file from src to dst, creating parent dirs and replacing dst atomically where possible.
func copyFileAtomic(src, dst string) error {
	in, err := os.Open(src)
	if err != nil {
		// if src does not exist, skip
		if os.IsNotExist(err) {
			return nil
		}
		return err
	}
	defer in.Close()
	if err := utils.EnsureDir(filepath.Dir(dst)); err != nil {
		return err
	}
	tmp := dst + ".tmp"
	out, err := os.Create(tmp)
	if err != nil {
		return err
	}
	_, err = io.Copy(out, in)
	cerr := out.Close()
	if err == nil {
		err = cerr
	}
	if err != nil {
		_ = os.Remove(tmp)
		return err
	}
	return os.Rename(tmp, dst)
}

// HasTempUpdate checks if there are updates in the temporary directory
func HasTempUpdate(ctx context.Context, options ...Option) (bool, error) {
	opts := applyOptions(options...)
	files := []string{
		model.DefaultProductListFileName,
		model.DefaultProductInfoFileName,
		model.DefaultProductAttrFileName,
		model.DefaultPriceTypeFileName,
		model.DefaultStockListFileName,
		model.DefaultPriceFileName,
		model.DefaultPriceVersionFileName,
	}
	for _, name := range files {
		bytes, err := opts.dualStore.Staging.Get(name)
		if err != nil {
			return true, nil
		}
		if len(bytes) > 0 {
			return true, nil
		}
	}

	return false, nil
}

// extractMD5 extracts md5 value from JSON bytes at top-level key "md5"
func extractMD5(b []byte) string {
	if len(b) == 0 {
		return ""
	}
	md5, err := jsonparser.GetString(b, "md5")
	if err != nil {
		return ""
	}
	return md5
}

// Internal implementation functions
// applyOptions applies defaults and user-provided options to produce a finalized Options
func applyOptions(options ...Option) *Options {
	// Defaults should mirror GetMenuData
	opts := &Options{
		host:           model.DefaultHost,
		prodDir:        "./prod",
		tempDir:        "./temp",
		mode:           ModeAutoMerge,
		maxRetries:     3,
		retryDelay:     500 * time.Millisecond,
		expireInterval: 7 * 24 * time.Hour,
		store:          store.NewFileStore("./"),
	}
	for _, opt := range options {
		opt(opts)
	}

	//dualStore
	opts.dualStore = store.NewDualStore(opts.store, opts.prodDir, opts.tempDir)
	return opts
}

// optionsToParams maps Options to service/menu.GetMenuParams

func optionsToParams(opts *Options) *menu.GetMenuParams {
	params := &menu.GetMenuParams{
		StoreID:          opts.storeID,
		Lang:             opts.lang,
		ChannelCode:      opts.channelCode,
		PartnerID:        opts.partnerID,
		Host:             opts.host,
		MaxRetries:       opts.maxRetries,
		RetryDelay:       opts.retryDelay,
		ReadStore:        opts.store,
		WriteStore:       opts.store,
		Token:            opts.token,
		ExpireInterval:   opts.expireInterval,
		NotFilterByPrice: opts.notFilterByPrice,
	}
	switch opts.mode {
	case ModeAutoMerge:
		params.WriteStore = opts.dualStore.Prod
		params.ReadStore = opts.dualStore.Prod
	case ModeFetchOnly:
		params.WriteStore = opts.dualStore.Staging
		params.ReadStore = opts.dualStore.Prod
	case ModeMergeTemp:
		params.WriteStore = opts.dualStore.Prod
		params.ReadStore = opts.dualStore.Staging
	case ModeProdOnly:
		params.WriteStore = opts.dualStore.Prod
		params.ReadStore = opts.dualStore.Prod

	}
	return params
}
func getMenuDataAutoMerge(ctx context.Context, options ...Option) (*model.ProductListResponse, error) {
	// For the initial minimal implementation, write/read directly to prodDir.
	// Later, this can fetch into tempDir and then merge.
	opts := applyOptions(options...)
	params := optionsToParams(opts)
	return menu.GetMenuDataLegacy(ctx, params)
}
